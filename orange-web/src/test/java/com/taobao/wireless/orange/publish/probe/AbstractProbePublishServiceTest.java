package com.taobao.wireless.orange.publish.probe;

import com.taobao.wireless.orange.BaseTest;
import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.external.wmcc.WmccPublishService;
import com.taobao.wmcc.client.constants.BriefTaskStatus;
import com.taobao.wmcc.client.publish.ConfigPublishRequest;
import com.taobao.wmcc.client.publish.PublishTaskInfo;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractProbePublishService 单元测试
 *
 * <AUTHOR>
 */
public class AbstractProbePublishServiceTest extends BaseTest {

    @Mock
    private WmccPublishService wmccPublishService;

    private TestProbePublishService probePublishService;

    // 测试数据
    private static final String TEST_APP_KEY_1 = "app1";
    private static final String TEST_APP_KEY_2 = "app2";
    private static final String TEST_APP_KEY_3 = "app3";
    private static final Long TEST_TASK_ID = 12345L;
    private static final Long TEST_CANCELED_TASK_ID = 67890L;
    private static final String TEST_CDN_DOMAIN = "test.cdn.com";

    @Before
    public void setUp() throws Exception {
        super.customSetUp();
        MockitoAnnotations.openMocks(this);
        probePublishService = new TestProbePublishService();
        probePublishService.wmccPublishService = wmccPublishService;
    }

    /**
     * 测试正常发布流程 - 包含紧急和非紧急应用
     */
    @Test
    public void testPublish_NormalFlow_WithBothEmergentAndNonEmergentApps() {
        // 准备测试数据
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.y,
                TEST_APP_KEY_2, Emergent.n,
                TEST_APP_KEY_3, Emergent.n
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 没有运行中的任务
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        // Mock 探针数据生成
        Map<String, TestProbe> probeData = Map.of(
                TEST_APP_KEY_1, createTestProbe(TEST_APP_KEY_1),
                TEST_APP_KEY_2, createTestProbe(TEST_APP_KEY_2),
                TEST_APP_KEY_3, createTestProbe(TEST_APP_KEY_3)
        );
        probePublishService.setProbeData(probeData);

        // Mock WMCC 发布
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_TASK_ID);
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.n))).thenReturn(TEST_TASK_ID + 1);

        // 执行测试
        probePublishService.publish();

        // 验证 WMCC 发布调用
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
        verify(wmccPublishService, times(2)).publishProbeToAserver(any(), eq(Emergent.n));

        // 验证探针任务ID更新
        assertThat(probePublishService.getUpdatedProbes()).hasSize(3);
        assertThat(probePublishService.getUpdatedTaskIds()).contains(TEST_TASK_ID, TEST_TASK_ID + 1, TEST_TASK_ID + 1);
    }

    /**
     * 测试紧急发布需要取消运行中任务的场景
     */
    @Test
    public void testPublish_EmergentAppsWithRunningTasks_ShouldCancelTasks() {
        // 准备测试数据
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.y,
                TEST_APP_KEY_2, Emergent.n
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 紧急应用有运行中的任务
        PublishTaskInfo runningTask = new PublishTaskInfo();
        runningTask.setTaskId(TEST_CANCELED_TASK_ID);
        runningTask.setStatus(BriefTaskStatus.RUNNING);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_1, AserverIndexType.SWITCH.getCode())).thenReturn(runningTask);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_2, AserverIndexType.SWITCH.getCode())).thenReturn(null);

        // Mock 探针数据生成
        Map<String, TestProbe> probeData = Map.of(
                TEST_APP_KEY_1, createTestProbe(TEST_APP_KEY_1),
                TEST_APP_KEY_2, createTestProbe(TEST_APP_KEY_2)
        );
        probePublishService.setProbeData(probeData);

        // Mock WMCC 发布
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_TASK_ID);
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.n))).thenReturn(TEST_TASK_ID + 1);

        // 执行测试
        probePublishService.publish();

        // 验证任务取消
        verify(wmccPublishService).cancelTask(TEST_CANCELED_TASK_ID);

        // 验证清除被取消任务的ID
        assertThat(probePublishService.getClearedTaskIds()).containsExactly(TEST_CANCELED_TASK_ID);
        assertThat(probePublishService.getClearedAppKeys()).containsExactly(TEST_APP_KEY_1);

        // 验证发布调用
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.n));

        // 验证探针任务ID更新
        assertThat(probePublishService.getUpdatedProbes()).hasSize(2);
        assertThat(probePublishService.getUpdatedTaskIds()).contains(TEST_TASK_ID, TEST_TASK_ID + 1);
    }

    /**
     * 测试非紧急应用过滤 - 有运行中任务时不发布
     */
    @Test
    public void testPublish_NonEmergentAppsWithRunningTasks_ShouldBeFiltered() {
        // 准备测试数据
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.n,
                TEST_APP_KEY_2, Emergent.n
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 一个非紧急应用有运行中的任务
        PublishTaskInfo runningTask = new PublishTaskInfo();
        runningTask.setTaskId(TEST_TASK_ID);
        runningTask.setStatus(BriefTaskStatus.RUNNING);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_1, AserverIndexType.SWITCH.getCode())).thenReturn(runningTask);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_2, AserverIndexType.SWITCH.getCode())).thenReturn(null);

        // Mock 探针数据生成 - 为所有应用生成数据，但只有没有运行任务的应用会被发布
        Map<String, TestProbe> probeData = Map.of(
                TEST_APP_KEY_1, createTestProbe(TEST_APP_KEY_1),
                TEST_APP_KEY_2, createTestProbe(TEST_APP_KEY_2)
        );
        probePublishService.setProbeData(probeData);

        // Mock WMCC 发布
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.n))).thenReturn(TEST_TASK_ID);

        // 执行测试
        probePublishService.publish();

        // 验证没有紧急发布
        verify(wmccPublishService, never()).publishProbeToAserver(any(), eq(Emergent.y));

        // 验证非紧急发布只被调用一次（只有 TEST_APP_KEY_2）
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.n));

        // 验证只有一个探针被更新（TEST_APP_KEY_2）
        assertThat(probePublishService.getUpdatedProbes()).hasSize(1);
        assertThat(probePublishService.getUpdatedTaskIds()).containsExactly(TEST_TASK_ID);
    }

    /**
     * 测试候选应用为空的场景
     */
    @Test
    public void testPublish_EmptyCandidateApps_ShouldNotPublish() {
        // 准备测试数据 - 空的候选应用
        probePublishService.setCandidateAppKeys(Map.of());

        // 执行测试
        probePublishService.publish();

        // 验证没有调用 WMCC 发布
        verify(wmccPublishService, never()).publishProbeToAserver(any(), any());
        verify(wmccPublishService, never()).cancelTask(anyLong());
    }

    /**
     * 测试探针数据为空的场景
     */
    @Test
    public void testPublish_EmptyProbeData_ShouldNotPublish() {
        // 准备测试数据
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.y
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 没有运行中的任务
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        // Mock 探针数据生成为空
        probePublishService.setProbeData(Map.of());

        // 执行测试
        probePublishService.publish();

        // 验证没有调用 WMCC 发布
        verify(wmccPublishService, never()).publishProbeToAserver(any(), any());
    }

    /**
     * 创建测试探针对象
     */
    private TestProbe createTestProbe(String appKey) {
        TestProbe probe = new TestProbe();
        probe.setAppKey(appKey);
        probe.setVersion("v1.0");
        probe.setContent("[{\"baseVersion\":\"v1.0\",\"resourceId\":\"test.json\",\"md5\":\"abc123\"}]");
        return probe;
    }

    /**
     * 测试应用重复时优先级处理 - 紧急应用优先
     */
    @Test
    public void testPublish_DuplicateApps_EmergentTakesPriority() {
        // 准备测试数据 - 同一个应用设置为紧急
        // 注意：新的实现中，Map<String, Emergent> 不会有重复的key，所以这个测试主要验证紧急应用的正常处理
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.y,
                TEST_APP_KEY_2, Emergent.n
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 没有运行中的任务
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        // Mock 探针数据生成
        Map<String, TestProbe> probeData = Map.of(
                TEST_APP_KEY_1, createTestProbe(TEST_APP_KEY_1),
                TEST_APP_KEY_2, createTestProbe(TEST_APP_KEY_2)
        );
        probePublishService.setProbeData(probeData);

        // Mock WMCC 发布
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_TASK_ID);
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.n))).thenReturn(TEST_TASK_ID + 1);

        // 执行测试
        probePublishService.publish();

        // 验证紧急发布和非紧急发布都被调用
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.n));

        // 验证两个探针都被更新
        assertThat(probePublishService.getUpdatedProbes()).hasSize(2);
        assertThat(probePublishService.getUpdatedTaskIds()).contains(TEST_TASK_ID, TEST_TASK_ID + 1);
    }

    /**
     * 测试任务状态为 FAILURE 时也被认为是运行中任务
     */
    @Test
    public void testPublish_TaskWithFailureStatus_ShouldBeTreatedAsRunning() {
        // 准备测试数据
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.y
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 紧急应用有失败状态的任务
        PublishTaskInfo failedTask = new PublishTaskInfo();
        failedTask.setTaskId(TEST_CANCELED_TASK_ID);
        failedTask.setStatus(BriefTaskStatus.FAILURE);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_1, AserverIndexType.SWITCH.getCode())).thenReturn(failedTask);

        // Mock 探针数据生成
        Map<String, TestProbe> probeData = Map.of(
                TEST_APP_KEY_1, createTestProbe(TEST_APP_KEY_1)
        );
        probePublishService.setProbeData(probeData);

        // Mock WMCC 发布
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_TASK_ID);

        // 执行测试
        probePublishService.publish();

        // 验证失败状态的任务也被取消
        verify(wmccPublishService).cancelTask(TEST_CANCELED_TASK_ID);
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));

        // 验证清除被取消任务的ID
        assertThat(probePublishService.getClearedTaskIds()).containsExactly(TEST_CANCELED_TASK_ID);
        assertThat(probePublishService.getClearedAppKeys()).containsExactly(TEST_APP_KEY_1);
    }

    /**
     * 测试 WMCC 配置生成的正确性
     */
    @Test
    public void testPublish_WmccConfigGeneration_ShouldGenerateCorrectFormat() {
        // 准备测试数据
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.y
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 没有运行中的任务
        when(wmccPublishService.getRunningPublishTaskInfo(anyString(), anyString())).thenReturn(null);

        // Mock 探针数据生成
        TestProbe testProbe = createTestProbe(TEST_APP_KEY_1);
        Map<String, TestProbe> probeData = Map.of(TEST_APP_KEY_1, testProbe);
        probePublishService.setProbeData(probeData);

        // Mock WMCC 发布
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_TASK_ID);

        // 执行测试
        probePublishService.publish();

        // 验证 WMCC 配置格式
        ArgumentCaptor<Collection<ConfigPublishRequest.Pair<String, String>>> configCaptor =
                ArgumentCaptor.forClass(Collection.class);
        verify(wmccPublishService).publishProbeToAserver(configCaptor.capture(), eq(Emergent.y));

        Collection<ConfigPublishRequest.Pair<String, String>> configs = configCaptor.getValue();
        assertThat(configs).hasSize(1);

        ConfigPublishRequest.Pair<String, String> config = configs.iterator().next();
        // 验证配置的基本结构存在
        assertThat(config).isNotNull();

        // 验证探针任务ID更新
        assertThat(probePublishService.getUpdatedProbes()).hasSize(1);
        assertThat(probePublishService.getUpdatedTaskIds()).containsExactly(TEST_TASK_ID);
    }

    /**
     * 调试测试 - 理解任务取消逻辑
     */
    @Test
    public void testPublish_Debug_TaskCancellationLogic() {
        // 准备测试数据 - 只有紧急应用
        Map<String, Emergent> candidateApps = Map.of(
                TEST_APP_KEY_1, Emergent.y
        );
        probePublishService.setCandidateAppKeys(candidateApps);

        // Mock 紧急应用有运行中的任务
        PublishTaskInfo runningTask = new PublishTaskInfo();
        runningTask.setTaskId(TEST_CANCELED_TASK_ID);
        runningTask.setStatus(BriefTaskStatus.RUNNING);
        when(wmccPublishService.getRunningPublishTaskInfo(TEST_APP_KEY_1, AserverIndexType.SWITCH.getCode())).thenReturn(runningTask);

        // Mock 探针数据生成
        Map<String, TestProbe> probeData = Map.of(
                TEST_APP_KEY_1, createTestProbe(TEST_APP_KEY_1)
        );
        probePublishService.setProbeData(probeData);

        // Mock WMCC 发布
        when(wmccPublishService.publishProbeToAserver(any(), eq(Emergent.y))).thenReturn(TEST_TASK_ID);

        // 执行测试
        probePublishService.publish();

        // 验证任务取消被调用
        verify(wmccPublishService).cancelTask(TEST_CANCELED_TASK_ID);

        // 验证发布被调用
        verify(wmccPublishService).publishProbeToAserver(any(), eq(Emergent.y));

        // 验证清除被取消任务的ID
        assertThat(probePublishService.getClearedTaskIds()).containsExactly(TEST_CANCELED_TASK_ID);
        assertThat(probePublishService.getClearedAppKeys()).containsExactly(TEST_APP_KEY_1);
    }

    /**
     * 测试用的探针数据类
     */
    private static class TestProbe {
        private String appKey;
        private String version;
        private String content;

        // getters and setters
        public String getAppKey() { return appKey; }
        public void setAppKey(String appKey) { this.appKey = appKey; }
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
    }

    /**
     * 测试用的 AbstractProbePublishService 实现
     */
    private static class TestProbePublishService extends AbstractProbePublishService<TestProbe> {
        private Map<String, Emergent> candidateAppKeys = new HashMap<>();
        private Map<String, TestProbe> probeData = new HashMap<>();
        private List<TestProbe> updatedProbes = new ArrayList<>();
        private List<Long> updatedTaskIds = new ArrayList<>();
        private List<Long> clearedTaskIds = new ArrayList<>();
        private List<String> clearedAppKeys = new ArrayList<>();

        public void setCandidateAppKeys(Map<String, Emergent> candidateAppKeys) {
            this.candidateAppKeys = candidateAppKeys;
        }

        public void setProbeData(Map<String, TestProbe> probeData) {
            this.probeData = probeData;
        }

        public List<TestProbe> getUpdatedProbes() { return updatedProbes; }
        public List<Long> getUpdatedTaskIds() { return updatedTaskIds; }
        public List<Long> getClearedTaskIds() { return clearedTaskIds; }
        public List<String> getClearedAppKeys() { return clearedAppKeys; }

        @Override
        protected Map<String, Emergent> getCandidateAppKeys() {
            return new HashMap<>(candidateAppKeys);
        }

        @Override
        protected TestProbe generateProbeData(String appKey) {
            return probeData.get(appKey);
        }

        @Override
        protected void updateProbeTaskId(TestProbe probe, Long taskId) {
            updatedProbes.add(probe);
            updatedTaskIds.add(taskId);
        }

        @Override
        protected void clearCanceledProbeTaskId(String appKey, Long canceledTaskId) {
            clearedAppKeys.add(appKey);
            clearedTaskIds.add(canceledTaskId);
        }

        @Override
        protected String getAppKey(TestProbe probe) {
            return probe.getAppKey();
        }

        @Override
        protected String getProbeVersion(TestProbe probe) {
            return probe.getVersion();
        }

        @Override
        protected String getProbeContent(TestProbe probe) {
            return probe.getContent();
        }

        @Override
        protected AserverIndexType getIndexType() {
            return AserverIndexType.SWITCH;
        }

        @Override
        protected String getCdnDomain() {
            return TEST_CDN_DOMAIN;
        }
    }
}
