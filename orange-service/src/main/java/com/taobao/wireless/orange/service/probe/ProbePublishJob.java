package com.taobao.wireless.orange.service.probe;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.wireless.orange.publish.probe.AbstractProbePublishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.StringJoiner;

@Component
@Slf4j(topic = "probe")
public class ProbePublishJob extends JavaProcessor {

    @Autowired
    private List<AbstractProbePublishService<?>> probePublishServices;

    @Override
    public ProcessResult process(JobContext context) {
        StringJoiner errorMessages = new StringJoiner("; ");

        for (AbstractProbePublishService<?> service : probePublishServices) {
            String serviceName = service.getClass().getSimpleName();
            try {
                service.publish();
                log.info("{} completed successfully", serviceName);
            } catch (Throwable e) {
                String errorMsg = serviceName + " error: " + e.getMessage();
                log.error("{} error", serviceName, e);
                errorMessages.add(errorMsg);
            }
        }

        String result = errorMessages.toString();
        boolean success = result.isEmpty();
        return new ProcessResult(success, result);
    }

    @Override
    public void kill(JobContext context) {
        // 任务超时被中断
        log.error("Probe_generate job killed");
        super.kill(context);
    }
}
