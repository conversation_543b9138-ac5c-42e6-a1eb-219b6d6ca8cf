package com.taobao.wireless.orange.publish.probe;

import com.alibaba.fastjson.JSON;
import com.taobao.mtop.commons.utils.CollectionUtil;
import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.common.constant.enums.Available;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.common.constant.enums.NamespaceVersionChangeType;
import com.taobao.wireless.orange.oswitch.dal.dao.ONamespaceVersionDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OProbeDAO;
import com.taobao.wireless.orange.oswitch.dal.dao.OResourceDAO;
import com.taobao.wireless.orange.oswitch.dal.entity.OIndexDO;
import com.taobao.wireless.orange.oswitch.dal.entity.ONamespaceVersionDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OProbeDO;
import com.taobao.wireless.orange.oswitch.dal.entity.OResourceDO;
import com.taobao.wireless.orange.publish.index.IndexGenerateService;
import com.taobao.wireless.orange.publish.probe.model.ProbeContentItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Switch 探针发布服务实现
 *
 * <AUTHOR>
 */
@Service
public class SwitchProbePublishService extends AbstractProbePublishService<OProbeDO> {

    @Autowired
    private IndexGenerateService indexGenerateService;

    @Autowired
    private ONamespaceVersionDAO namespaceVersionDAO;

    @Autowired
    private OProbeDAO probeDAO;

    @Autowired
    private OResourceDAO resourceDAO;

    @Value("${orange.switch.cdn.domain}")
    private String cdnDomain;

    @Override
    protected Map<String, Emergent> getCandidateAppKeys() {
        return namespaceVersionDAO.lambdaQuery()
                .select(ONamespaceVersionDO::getAppKey, ONamespaceVersionDO::getIsEmergent)
                .isNull(ONamespaceVersionDO::getIndexVersion)
                .ne(ONamespaceVersionDO::getChangeType, NamespaceVersionChangeType.FINISH_NO_IMPACT_RELEASE)
                .list()
                .stream()
                .distinct()
                .collect(Collectors.toMap(ONamespaceVersionDO::getAppKey,
                        nv -> Optional.ofNullable(nv.getIsEmergent()).orElse(Emergent.n),
                        (a, b) -> Emergent.y.equals(a) ? a : b));
    }

    /**
     * 更新被取消探针任务的任务ID为新任务的ID
     *
     * @param appKey         本次发布的应用
     * @param canceledTaskId 被取消的任务ID
     */
    @Override
    protected void clearCanceledProbeTaskId(String appKey, Long canceledTaskId) {
        probeDAO.lambdaUpdate()
                .eq(OProbeDO::getAppKey, appKey)
                .eq(OProbeDO::getAgatewareTaskId, canceledTaskId.toString())
                .set(OProbeDO::getAgatewareTaskId, null)
                .update();
    }

    @Override
    protected OProbeDO generateProbeData(String appKey) {
        List<ONamespaceVersionDO> namespaceVersions = namespaceVersionDAO.getAvailableNamespaceVersionsByAppKey(appKey);
        // 探针索引版本号即为最大的 namespaceChangeVersion
        String indexVersion = getMaxNamespaceChangeVersion(namespaceVersions);

        List<OIndexDO> indexDOs = indexGenerateService.generate(appKey, namespaceVersions, indexVersion);
        if (CollectionUtil.isEmpty(indexDOs)) {
            return null;
        }

        return OProbeDO.builder()
                .appKey(appKey)
                .indexVersion(indexVersion)
                .isAvailable(Available.y)
                .content(buildProbeContent(indexDOs))
                .build();
    }

    @Override
    @Transactional
    public void updateProbeTaskId(OProbeDO probe, Long taskId) {
        // 将应用历史探针失效
        probeDAO.lambdaUpdate()
                .eq(OProbeDO::getAppKey, probe.getAppKey())
                .set(OProbeDO::getIsAvailable, Available.n)
                .update();

        // 插入最新的探针记录
        probe.setAgatewareTaskId(taskId.toString());
        probeDAO.save(probe);

        // 更新 namespaceVersionDO 记录的索引版本号
        namespaceVersionDAO.lambdaUpdate()
                .eq(ONamespaceVersionDO::getAppKey, probe.getAppKey())
                .isNull(ONamespaceVersionDO::getIndexVersion)
                .le(ONamespaceVersionDO::getNamespaceChangeVersion, probe.getIndexVersion())
                .set(ONamespaceVersionDO::getIndexVersion, probe.getIndexVersion())
                .update();
    }

    @Override
    protected String getAppKey(OProbeDO probe) {
        return probe.getAppKey();
    }

    @Override
    protected String getProbeVersion(OProbeDO probe) {
        return probe.getIndexVersion();
    }

    @Override
    protected String getProbeContent(OProbeDO probe) {
        return probe.getContent();
    }

    @Override
    protected AserverIndexType getIndexType() {
        return AserverIndexType.SWITCH;
    }

    @Override
    protected String getCdnDomain() {
        return cdnDomain;
    }

    /**
     * 根据索引列表生成探针内容
     *
     * @param indices 索引列表
     * @return 探针内容
     */
    private String buildProbeContent(List<OIndexDO> indices) {
        var resourceIds = indices.stream().map(OIndexDO::getIndexResourceId).collect(Collectors.toList());
        Map<String, String> resourceId2Md5 = resourceDAO.lambdaQuery()
                .in(OResourceDO::getResourceId, resourceIds)
                .list()
                .stream()
                .collect(Collectors.toMap(OResourceDO::getResourceId, OResourceDO::getMd5));

        var contentItems = indices.stream()
                .sorted(Comparator.comparing(OIndexDO::getIndexVersion).reversed())
                .map(index -> ProbeContentItem.builder()
                        .baseVersion(index.getBaseIndexVersion())
                        .md5(resourceId2Md5.get(index.getIndexResourceId()))
                        .resourceId(index.getIndexResourceId())
                        .build()
                ).collect(Collectors.toList());
        return JSON.toJSONString(contentItems);
    }

    private String getMaxNamespaceChangeVersion(List<ONamespaceVersionDO> namespaceVersions) {
        return namespaceVersions.stream()
                .map(ONamespaceVersionDO::getNamespaceChangeVersion)
                .map(Long::parseLong)
                .max(Long::compareTo)
                .map(String::valueOf)
                .orElse(null);
    }
}
