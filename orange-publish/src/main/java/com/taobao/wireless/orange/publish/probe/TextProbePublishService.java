package com.taobao.wireless.orange.publish.probe;

import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.external.switchcenter.SwitchConfig;
import com.taobao.wireless.orange.text.dal.dao.ProbeDAO;
import com.taobao.wireless.orange.text.dal.dao.ProbeTaskDAO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskDO;
import com.taobao.wireless.orange.text.dal.entity.ProbeTaskExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * Text 探针发布服务实现
 *
 * <AUTHOR>
 */
@Service
public class TextProbePublishService extends AbstractProbePublishService<ProbeTaskExt> {

    @Autowired
    private ProbeDAO probeDAO;

    @Autowired
    private ProbeTaskDAO probeTaskDAO;

    @Value("${orange.text.cdn.domain}")
    private String cdnDomain;

    /**
     * probe-5 对应的发布类型(即包含百分比灰度的发布产物)
     */
    private static final String PROBE_5_PUBLISH_TYPE = "GRAY";

    @Override
    protected Map<String, Emergent> getCandidateAppKeys() {
        // 获取需要发布探针的应用列表
        List<ProbeTaskDO> candidateProbeTasks = new ArrayList<>(probeTaskDAO.getAgatewareCandidateProbeTasks());

        // 追加强制发布应用列表（用于新老探针不匹配时进行强制修复）
        candidateProbeTasks.removeIf(probeTaskDO -> SwitchConfig.forcePublishTextProbeAppKeys.contains(probeTaskDO.getAppKey()));
        candidateProbeTasks.addAll(SwitchConfig.forcePublishTextProbeAppKeys.stream()
                .map(appKey -> {
                    ProbeTaskDO probeTaskDO = new ProbeTaskDO();
                    probeTaskDO.setAppKey(appKey);
                    probeTaskDO.setIsEmergent(Emergent.y);
                    return probeTaskDO;
                })
                .toList());

        return candidateProbeTasks.stream()
                .distinct()
                .collect(Collectors.toMap(ProbeTaskDO::getAppKey,
                        v -> Optional.ofNullable(v.getIsEmergent()).orElse(Emergent.n),
                        (a, b) -> Emergent.y.equals(a) ? a : b));
    }

    /**
     * 更新被取消探针任务的任务ID为新任务的ID
     *
     * @param appKey         本次发布的应用
     * @param canceledTaskId 被取消的任务ID
     */
    @Override
    protected void clearCanceledProbeTaskId(String appKey, Long canceledTaskId) {
        probeTaskDAO.lambdaUpdate()
                .eq(ProbeTaskDO::getAppKey, appKey)
                .eq(ProbeTaskDO::getAgatewareTaskId, canceledTaskId)
                .set(ProbeTaskDO::getAgatewareTaskId, null)
                .update();
    }

    @Override
    protected Map<String, ProbeTaskExt> generateProbeData(Set<String> appKeys) {
        return probeDAO.getAvailableProbesWithMaxTaskID(new ArrayList<>(appKeys)).stream()
                .collect(Collectors.toMap(ProbeTaskExt::getAppKey, Function.identity(),
                        (ProbeTaskExt probe1, ProbeTaskExt probe2) -> {
                            // changeVersion 相同的情况下，优先选择 probe5 对应的发布类型（即包含百分比灰度的发布产物）
                            if (probe1.getChangeVersion().equals(probe2.getChangeVersion())) {
                                return PROBE_5_PUBLISH_TYPE.equals(probe1.getPublishType()) ? probe1 : probe2;
                            }

                            return probe1.getChangeVersion().compareTo(probe2.getChangeVersion()) > 0 ? probe1 : probe2;
                        }));
    }

    @Override
    protected void updateProbeTaskId(ProbeTaskExt probe, Long taskId) {
        probeTaskDAO.lambdaUpdate()
                .eq(ProbeTaskDO::getAppKey, probe.getAppKey())
                .le(ProbeTaskDO::getId, Optional.ofNullable(probe.getMaxTaskId()).orElse(0L))
                .isNull(ProbeTaskDO::getAgatewareTaskId)
                .set(ProbeTaskDO::getAgatewareTaskId, taskId)
                .update();
    }

    @Override
    protected String getAppKey(ProbeTaskExt probe) {
        return probe.getAppKey();
    }

    @Override
    protected String getProbeVersion(ProbeTaskExt probe) {
        return probe.getIndexVersion();
    }

    @Override
    protected String getProbeContent(ProbeTaskExt probe) {
        return probe.getMetas();
    }

    @Override
    protected AserverIndexType getIndexType() {
        return AserverIndexType.DP;
    }

    @Override
    protected String getCdnDomain() {
        return cdnDomain;
    }
}