package com.taobao.wireless.orange.publish.probe;

import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.external.wmcc.WmccPublishService;
import com.taobao.wireless.orange.publish.probe.model.AserverProbe;
import com.taobao.wmcc.client.constants.BriefTaskStatus;
import com.taobao.wmcc.client.publish.ConfigPublishRequest;
import com.taobao.wmcc.client.publish.PublishTaskInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 探针发布事务服务
 * 用于解决 @Transactional 自调用问题
 *
 * <AUTHOR>
 */
@Slf4j(topic = "probe")
@Service
public class ProbePublishTransactionService {

    @Autowired
    private WmccPublishService wmccPublishService;

    /**
     * 为单个应用发布探针（带事务）
     *
     * @param probePublishService 探针发布服务
     * @param appKey              应用Key
     * @param emergent            紧急程度
     */
    @Transactional
    public <T> void publishProbeForAppKey(AbstractProbePublishService<T> probePublishService, String appKey, Emergent emergent) {
        try {
            Long publishingTaskId = this.getAppKeyPublishingTaskId(probePublishService, appKey, emergent);
            // 非紧急应用有正在发布的任务时，跳过发布
            if (Emergent.n.equals(emergent) && publishingTaskId != null) {
                log.info("{}_appKey_is_publishing, emergent: {}, appKey: {}, taskId: {}", 
                        probePublishService.getIndexType().name(), emergent.name(), appKey, publishingTaskId);
                return;
            }

            // 紧急应用有正在发布的任务时，取消任务
            if (Emergent.y.equals(emergent) && publishingTaskId != null) {
                wmccPublishService.cancelTask(publishingTaskId);
                probePublishService.clearCanceledProbeTaskId(appKey, publishingTaskId);
                log.info("{}_cancel_task_success, emergent: {}, appKey: {}, taskId: {}", 
                        probePublishService.getIndexType().name(), emergent.name(), appKey, publishingTaskId);
            }

            // 生成探针数据（只有要发布 wmcc 的任务才调用该方法）
            T probe = probePublishService.generateProbeData(appKey);
            if (probe == null) {
                log.error("{}_probe_data_is_empty, emergent: {}, appKey: {}", 
                        probePublishService.getIndexType().name(), emergent.name(), appKey);
                return;
            }

            // 发布探针到 WMCC
            Long taskId = wmccPublishService.publishProbeToAserver(
                    probePublishService.generateProbeWmccConfig(probe), emergent);
            log.info("{}_publish_probe_to_aserver_success, emergent: {}, appKey: {}, taskId: {}", 
                    probePublishService.getIndexType().name(), emergent.name(), appKey, taskId);

            // 更新探针任务ID
            probePublishService.updateProbeTaskId(probe, taskId);
            log.info("{}_update_probe_task_id_success, emergent: {}, appKey: {}, taskId: {}", 
                    probePublishService.getIndexType().name(), emergent.name(), appKey, taskId);
        } catch (Exception e) {
            log.error("{}_publish_probe_error, emergent: {}, appKey: {}, error: {}", 
                    probePublishService.getIndexType().name(), emergent.name(), appKey, e.getMessage(), e);
            // 如果调用 wmcc 方法失败或异常，进行 generateProbeData 的数据事务回滚
            // 由于该方法使用了 @Transactional 注解，异常会自动触发事务回滚
            throw new RuntimeException("Probe publish failed for appKey: " + appKey, e);
        }
    }

    private <T> Long getAppKeyPublishingTaskId(AbstractProbePublishService<T> probePublishService, String appKey, Emergent emergent) {
        PublishTaskInfo taskInfo = wmccPublishService.getRunningPublishTaskInfo(appKey, probePublishService.getIndexType().getCode());
        boolean isPublishing = taskInfo != null &&
                (BriefTaskStatus.RUNNING.equals(taskInfo.getStatus()) ||
                        BriefTaskStatus.FAILURE.equals(taskInfo.getStatus()));
        log.info("{}_get_running_publish_task_info, emergent: {}, appKey: {}, isPublishing: {}", 
                probePublishService.getIndexType().name(), emergent.name(), appKey, isPublishing);
        return isPublishing ? taskInfo.getTaskId() : null;
    }
}
